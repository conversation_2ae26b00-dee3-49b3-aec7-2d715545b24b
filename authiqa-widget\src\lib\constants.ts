function detectEnvironment(): 'staging' | 'production' {
    const scriptElement = document.querySelector('script[data-public-key]');
    const scriptSrc = scriptElement?.getAttribute('src') || '';

    if (scriptSrc.includes('staging.widget.authiqa.com')) {
        return 'staging';
    }
    return 'production';
}

function getApiBase(): string {
    const environment = detectEnvironment();
    return environment === 'staging'
        ? 'https://staging.api.authiqa.com'
        : 'https://api.authiqa.com';
}

export const API_ENDPOINTS = {
    ORGANIZATION_DETAILS: `${getApiBase()}/auth/organization-details`,
    AUTH_STATUS: `${getApiBase()}/auth/status`
};

export const THEMES = {
    light: {
        background: '#ffffff',
        text: '#000000',
        border: '#e0e0e0',
        modalOverlay: 'rgba(0, 0, 0, 0.5)',
        labelColor: '#333333'
    },
    dark: {
        background: '#1a1a1a',
        text: '#ffffff',
        border: '#333333',
        modalOverlay: 'rgba(0, 0, 0, 0.7)',
        labelColor: '#ffffff'
    }
};

export const STYLE_CONSTANTS = {
    STYLE_ELEMENT_ID: 'authiqa-styles',
    CONTAINER_CLASS: 'authiqa-container',
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark'
    }
}; 

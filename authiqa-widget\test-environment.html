<!DOCTYPE html>
<html>
<head>
    <title>Environment Detection Test</title>
</head>
<body>
    <h1>Environment Detection Test</h1>
    <div id="test-results"></div>
    
    <script>
        // Test environment detection function
        function detectEnvironment() {
            const scriptElement = document.querySelector('script[data-public-key]');
            const scriptSrc = scriptElement?.getAttribute('src') || '';
            
            if (scriptSrc.includes('staging.widget.authiqa.com')) {
                return 'staging';
            }
            return 'production';
        }
        
        // Test with staging URL
        function testStagingDetection() {
            // Create a mock script element for staging
            const stagingScript = document.createElement('script');
            stagingScript.setAttribute('data-public-key', 'test-key');
            stagingScript.setAttribute('src', 'https://staging.widget.authiqa.com/index.js');
            document.head.appendChild(stagingScript);
            
            const env = detectEnvironment();
            document.getElementById('test-results').innerHTML += `<p>Staging test: ${env === 'staging' ? '✅ PASS' : '❌ FAIL'} (detected: ${env})</p>`;
            
            // Clean up
            stagingScript.remove();
        }
        
        // Test with production URL
        function testProductionDetection() {
            // Create a mock script element for production
            const prodScript = document.createElement('script');
            prodScript.setAttribute('data-public-key', 'test-key');
            prodScript.setAttribute('src', 'https://widget.authiqa.com/index.js');
            document.head.appendChild(prodScript);
            
            const env = detectEnvironment();
            document.getElementById('test-results').innerHTML += `<p>Production test: ${env === 'production' ? '✅ PASS' : '❌ FAIL'} (detected: ${env})</p>`;
            
            // Clean up
            prodScript.remove();
        }
        
        // Run tests
        testStagingDetection();
        testProductionDetection();
        
        // Test API base URL generation
        function getApiBase(environment) {
            return environment === 'staging' 
                ? 'https://staging.api.authiqa.com'
                : 'https://api.authiqa.com';
        }
        
        document.getElementById('test-results').innerHTML += `<h3>API Base URLs:</h3>`;
        document.getElementById('test-results').innerHTML += `<p>Staging: ${getApiBase('staging')}</p>`;
        document.getElementById('test-results').innerHTML += `<p>Production: ${getApiBase('production')}</p>`;
    </script>
</body>
</html>

import { getApiConfig } from './config';
import { AuthUrls, AuthResponse, WidgetConfig, OrganizationDetailsResponse } from './types';
import { validateCustomAuthPaths } from './validation';
export class ApiService {
    private publicKey: string; // Changed from apiKey
    private config: ReturnType<typeof getApiConfig>;
    private verifyAuthPath?: string;
    private updatePasswordPath?: string;
    private resendAuthPath?: string;
    private successAuthPath?: string;
    private signinAuthPath?: string;

    constructor(widgetConfig: WidgetConfig) {
        this.publicKey = widgetConfig.publicKey; // Changed from apiKey
        this.config = getApiConfig(widgetConfig.organizationDomain);
        
        // Only validate that the URLs are properly formatted
        const validation = validateCustomAuthPaths({
            verifyAuthPath: widgetConfig.verifyAuthPath,
            updatePasswordPath: widgetConfig.updatePasswordPath,
            resendAuthPath: widgetConfig.resendAuthPath,
            successAuthPath: widgetConfig.successAuthPath,
            signinAuthPath: widgetConfig.signinAuthPath
        }, widgetConfig.organizationDomain);

        if (validation.isValid) {
            // Store all custom paths
            this.verifyAuthPath = widgetConfig.verifyAuthPath;
            this.updatePasswordPath = widgetConfig.updatePasswordPath;
            this.resendAuthPath = widgetConfig.resendAuthPath;
            this.successAuthPath = widgetConfig.successAuthPath;
            this.signinAuthPath = widgetConfig.signinAuthPath;
        }
    }

    async signup(formData: any): Promise<Response> {
        const payload = {
            ...formData,
            verifyAuthPath: this.verifyAuthPath // Always include it if it exists
        };
        

        return fetch(`${this.config.API_BASE}/auth/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Public-Key': this.publicKey // Changed from 'X-Api-Key'
            },
            body: JSON.stringify(payload)
        });
    }

    async resetPassword(formData: any): Promise<Response> {
        const payload = {
            ...formData,
            updatePasswordPath: this.updatePasswordPath // Always include it if it exists
        };


        return fetch(`${this.config.API_BASE}/auth/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Public-Key': this.publicKey // Changed from 'X-Api-Key'
            },
            body: JSON.stringify(payload)
        });
    }

    async resendConfirmation(formData: any): Promise<Response> {
        const payload = {
            ...formData,
            verifyAuthPath: this.verifyAuthPath // Always include it if it exists
        };


        return fetch(`${this.config.API_BASE}/auth/request-new-confirmation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Public-Key': this.publicKey // Changed from 'X-Api-Key'
            },
            body: JSON.stringify(payload)
        });
    }

    getApiBase(): string {
        return this.config.API_BASE;
    }

    async getOrganizationDetails(): Promise<OrganizationDetailsResponse> {
        const url = `${this.config.API_BASE}/auth/organization-details`;
        
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'X-Public-Key': this.publicKey, // Changed from 'X-Api-Key'
                    'Content-Type': 'application/json'
                }
            });


            if (!response.ok) {
                throw new Error(`API Error: ${response.statusText}`);
            }

            const parsedResponse = JSON.parse(await response.text());
            
            // Extract data from the wrapped response
            if (parsedResponse.success && parsedResponse.data) {
                return parsedResponse.data;
            }

            throw new Error('Invalid response format from server');
        } catch (error) {
            console.error('Organization Details Request Failed', error);
            throw error;
        }
    }

    async checkAuthStatus(): Promise<AuthResponse> {
        const response = await fetch(
            `${this.config.API_BASE}${this.config.ENDPOINTS.AUTH_STATUS}`,
            {
                headers: {
                    'X-Public-Key': this.publicKey, // Changed from 'X-Api-Key'
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`API Error: ${response.statusText}`);
        }

        return response.json();
    }

   
}

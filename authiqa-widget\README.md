# Authiqa Widget

A customizable authentication widget for integrating Authiqa authentication into your web applications.

## Features
- Seamless authentication integration
- Customizable themes (light/dark)
- Popup and redirect modes
- Complete white-labeling support
- Event-driven architecture
- TypeScript support
- Responsive design

## Test Coverage

![Coverage Statements](./badges/badge-statements.svg)
![Coverage Branches](./badges/badge-branches.svg)
![Coverage Functions](./badges/badge-functions.svg)
![Coverage Lines](./badges/badge-lines.svg)

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

You can install the widget via npm:

bash
npm install @authiqa/widget

Or use it directly via CDN:

html
<script src="https://widget.authiqa.com/latest/authiqa-widget.js"></script>

### Building from Source

1. Install dependencies:
bash
npm install

2. Build the widget:
bash
npm run build

This will generate a single JavaScript file at `dist/authiqa-widget.js` that you can include in your web application.

## Integration

1. Include the widget script in your HTML:

html
<script src="https://widget.authiqa.com/latest/authiqa-widget.js"></script>


2. Create a container element:

html
<div id="auth-container"></div>

3. Initialize the widget:

javascript
const widget = new Authiqa.AuthiqaWidget({
public: 'your_public_key_here',
container: 'auth-container',
mode: 'popup',
theme: 'light',
organizationDomain: 'auth.yourcompany.com'
});
await widget.initialize();

4. Show authentication forms:
javascript
// Show login form
widget.show('signinUrl');
// Show signup form
widget.show('signupUrl');

5. Handle authentication events:
javascript
widget.on('widget:success', (data) => {
console.log('Authentication successful:', data);
});
widget.on('widget:error', (error) => {
console.error('Authentication failed:', error);
});

## Complete HTML Example
html
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Authiqa Widget Example</title>
</head>
<body>
<div id="auth-container"></div>
<button onclick="showLogin()">Login</button>
<button onclick="showSignup()">Sign Up</button>
<script src="https://widget.authiqa.com/latest/authiqa-widget.js"></script>
<script>
const widget = new Authiqa.AuthiqaWidget({
public: 'your_public_key_here',
container: 'auth-container',
mode: 'popup',
theme: 'light',
organizationDomain: 'auth.yourcompany.com'
});
widget.initialize().then(() => {
console.log('Widget initialized');
}).catch(error => {
console.error('Widget initialization failed:', error);
});
// Listen for auth events
widget.on('widget:success', (data) => {
console.log('Authentication successful:', data);
});
widget.on('widget:error', (error) => {
console.error('Authentication failed:', error);
});
function showLogin() {
widget.show('signin');
}
function showSignup() {
widget.show('signup');
}
</script>
</body>
</html>

## Configuration Options

| Option | Type | Required | Default | Description |
|--------|------|----------|---------|-------------|
| publicKey | string | Yes | - | Your AuthIQA public key |
| container | string | Yes | - | ID of the container element |
| mode | 'popup' \| 'redirect' | No | 'popup' | Authentication display mode |
| theme | 'light' \| 'dark' | No | 'light' | Widget theme |
| organizationDomain | string | Yes | - | Your organization's domain for white-labeling |

## Development

Run development build with watch mode:
bash
npm run dev

Run tests:
bash
npm test
 
Run tests with coverage:
bash
npm run test:coverage

## Available Authentication Actions

- `signin`: Show sign-in form
- `signup`: Show sign-up form
- `emailVerification`: Show email verification
- `resetPassword`: Show password reset
- `updatePassword`: Show password update
- `resendVerification`: Resend verification email
- `successful`: URL to redirect after successful authentication (used internally)

## White-Labeling

The widget supports complete white-labeling of the authentication experience. All URLs and branding will appear under your organization's domain, maintaining a seamless user experience.

## Security

The widget is designed with security in mind:
- All API calls are made over HTTPS
- Authentication tokens are securely handled
- Cross-Origin Resource Sharing (CORS) protection
- Content Security Policy (CSP) compliant

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT License

## Hash-Based Navigation

The widget supports hash-based navigation, allowing users to switch between different authentication forms by changing the URL hash.

### Usage

Include the hash navigation script after the main widget script:

```html
<!-- Include the Authiqa widget -->
<script
    src="https://widget.authiqa.com/latest/index.js"
    defer
    data-public-key="YOUR_public_KEY"
></script>

<!-- Include the hash navigation script -->
<script src="https://widget.authiqa.com/latest/hash-navigation.js" defer></script>
```

### Supported Hash Values

- `#signin` - Shows the sign-in form
- `#signup` - Shows the sign-up form
- `#verify` - Shows the email verification form
- `#reset` - Shows the password reset form
- `#update` - Shows the password update form
- `#resend` - Shows the resend confirmation form

### Example

Create navigation links in your application:

```html
<a href="#signin">Sign In</a>
<a href="#signup">Sign Up</a>
<a href="#reset">Forgot Password?</a>
```

When a user clicks on these links, the widget will automatically show the corresponding form.

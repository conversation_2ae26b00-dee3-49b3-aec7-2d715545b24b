// Hash-based navigation for Authiqa Widget
document.addEventListener('DOMContentLoaded', function() {
  // Wait for the widget to be loaded
  setTimeout(function() {
    if (typeof window.AuthiqaWidget !== 'function') {
      console.error('AuthiqaWidget not properly loaded');
      return;
    }
    
    // Get widget instance (or create a new one if needed)
    let widget: any;
    
    // Get the original action from the script tag
    const scriptElement = document.querySelector('script[data-public-key]'); // Changed from data-api-key
    const originalAction = scriptElement?.getAttribute('action') || 'signin';
    
    // Function to get or create widget
    function getWidget() {
      if (!widget) {
        // Create widget with your configuration
        widget = new window.AuthiqaWidget({
          publicKey: document.querySelector('script[data-public-key]')?.getAttribute('data-public-key') || '', // Changed from data-api-key
          container: 'authiqa',
          mode: 'popup',
          theme: document.querySelector('script[theme]')?.getAttribute('theme') || 'light',
          organizationDomain: 'authiqa.com',
          // Get paths from script attributes if they exist
          verifyAuthPath: document.querySelector('script[verifyAuthPath]')?.getAttribute('verifyAuthPath'),
          resendAuthPath: document.querySelector('script[resendAuthPath]')?.getAttribute('resendAuthPath'),
          successAuthPath: document.querySelector('script[successAuthPath]')?.getAttribute('successAuthPath'),
          signinAuthPath: document.querySelector('script[signinAuthPath]')?.getAttribute('signinAuthPath'),
          resetAuthPath: document.querySelector('script[resetAuthPath]')?.getAttribute('resetAuthPath')
        });
      }
      return widget;
    }
    
    // Function to show form based on hash
    function handleHashChange() {
      const hash = window.location.hash.substring(1); // Remove the # character
      const validActions = ['signin', 'signup', 'verify', 'reset', 'update', 'resend'];
      
      if (validActions.includes(hash)) {
        getWidget().show(hash);
      } else if (hash === '') {
        // No hash or empty hash, respect the original action attribute
        getWidget().show(originalAction);
      }
    }
    
    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);
    
    // Initial check when page loads
    handleHashChange();
    
  }, 500); // Small delay to ensure widget script is loaded
});

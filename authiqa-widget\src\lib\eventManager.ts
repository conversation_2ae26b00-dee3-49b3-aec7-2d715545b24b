type EventCallback = (data?: any) => void;

export class EventManager {
    private static listeners: Map<string, EventCallback[]> = new Map();

    static subscribe(event: string, callback: EventCallback): void {
        const listeners = this.listeners.get(event) || [];
        listeners.push(callback);
        this.listeners.set(event, listeners);
    }

    static unsubscribe(event: string, callback: EventCallback): void {
        const listeners = this.listeners.get(event) || [];
        const index = listeners.indexOf(callback);
        if (index > -1) {
            listeners.splice(index, 1);
            this.listeners.set(event, listeners);
        }
    }

    static emit(event: string, data?: any): void {
        const listeners = this.listeners.get(event) || [];
        listeners.forEach(callback => callback(data));
    }
}

// Add these events to handle style-related changes
export const STYLE_EVENTS = {
    THEME_CHANGED: 'THEME_CHANGED',
    STYLES_DISABLED: 'STYLES_DISABLED',
    STYLES_ENABLED: 'STYLES_ENABLED'
}; 
import { AuthUrls } from '../lib/types';

export class AuthFrame {
  private container: HTMLElement;
  private iframe: HTMLIFrameElement;
  private authUrls: AuthUrls;
  private config: any;

  constructor(containerId: string, authUrls: AuthUrls, styles: any = {}, config: any) {
    const container = document.getElementById(containerId);
    if (!container) {
      throw new Error(`Container with id ${containerId} not found`);
    }
    this.container = container;
    this.authUrls = authUrls;
    this.iframe = this.createIframe(styles);
    this.config = config;
  }

  private createIframe(styles: any): HTMLIFrameElement {
    const iframe = document.createElement('iframe');
    Object.assign(iframe.style, {
      width: '100%',
      height: '600px',
      border: 'none',
      ...styles
    });
    return iframe;
  }

  public loadAuthPage(type: keyof AuthUrls): void {
    const originalUrl = new URL(this.authUrls[type]);
    const whitelabeledUrl = new URL(originalUrl.pathname, `https://${this.config.organizationDomain}`);
    this.iframe.src = whitelabeledUrl.toString();
    this.container.appendChild(this.iframe);
  }

  public remove(): void {
    this.iframe.remove();
  }
}

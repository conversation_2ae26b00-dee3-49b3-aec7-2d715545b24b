import { AuthUrls } from './types';

interface ValidationResult {
  isValid: boolean;
  message: string;
}

export function validateAuthUrls(authUrls: AuthUrls, organizationUrl: string): ValidationResult {
    if (!authUrls) {
        return {
            isValid: false,
            message: 'Authentication URLs are required'
        };
    }

    const requiredUrls = [
        'signup',
        'signin',
        'verify',
        'reset',
        'update',
        'resend',
        'successful'
    ];

    for (const urlField of requiredUrls) {
        if (!authUrls[urlField as keyof AuthUrls]) {
            return {
                isValid: false,
                message: `${urlField} is required`
            };
        }

        try {
            const url = new URL(authUrls[urlField as keyof AuthUrls]);
            if (url.protocol !== 'https:') {
                return {
                    isValid: false,
                    message: `${urlField} must use HTTPS protocol`
                };
            }
        } catch {
            return {
                isValid: false,
                message: `Invalid URL format for ${urlField}`
            };
        }
    }

    return {
        isValid: true,
        message: ''
    };
}

export function validateCustomAuthPaths(paths: { 
    verifyAuthPath?: string, 
    updatePasswordPath?: string,
    resendAuthPath?: string,
    successAuthPath?: string,
    signinAuthPath?: string
}, organizationDomain: string): ValidationResult {
    for (const [key, path] of Object.entries(paths)) {
        if (path) {
            try {
                const url = new URL(path);
                
                // Must use HTTPS
                if (url.protocol !== 'https:') {
                    return {
                        isValid: false,
                        message: `${key} must use HTTPS protocol`
                    };
                }
                
                // Remove the domain validation check entirely
                // No longer checking if URL matches organization domain
            } catch {
                return {
                    isValid: false,
                    message: `${key} must be a complete URL (e.g., https://domain.com/path)`
                };
            }
        }
    }

    return {
        isValid: true,
        message: ''
    };
}

import { AuthUrls, WidgetConfig } from '../lib/types';
import { AuthFrame } from './AuthFrame';

export class Modal {
  private modal: HTMLDivElement;
  private frame: AuthFrame;
  private isOpen: boolean = false;

  constructor(authUrls: AuthUrls, styles: any, config: WidgetConfig) {
    this.modal = this.createModal(styles);
    this.frame = new AuthFrame('authiqa-frame', authUrls, styles.iframe, config);
  }

  private createModal(styles: any): HTMLDivElement {
    const modal = document.createElement('div');
    Object.assign(modal.style, styles.overlay);

    const container = document.createElement('div');
    container.id = 'authiqa-frame';
    Object.assign(container.style, styles.container);

    modal.appendChild(container);
    document.body.appendChild(modal);
    return modal;
  }

  public show(type: keyof AuthUrls): void {
    this.modal.style.display = 'block';
    this.frame.loadAuthPage(type);
    this.isOpen = true;
  }

  public hide(): void {
    this.modal.style.display = 'none';
    this.frame.remove();
    this.isOpen = false;
  }
}

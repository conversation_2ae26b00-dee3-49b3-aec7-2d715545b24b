const path = require('path');

module.exports = {
  entry: {
    index: './src/index.ts',
    'hash-navigation': './src/hash-navigation.ts'
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.css'],
  },
  output: {
    filename: '[name].js',
    path: path.resolve(__dirname, 'dist'),
    library: 'Authiqa',
    libraryTarget: 'umd',
    globalObject: 'this'
  },
};

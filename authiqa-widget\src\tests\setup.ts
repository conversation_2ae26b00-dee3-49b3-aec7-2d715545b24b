// Declare module augmentation for global
export {};

declare global {
  namespace NodeJS {
    interface Global {
      WIDGET_URL: string;
    }
  }
}

// Mock fetch globally
global.fetch = jest.fn();

// Mock DOM elements
global.document.getElementById = jest.fn();

// Define production widget URL globally
(global as any).WIDGET_URL = 'https://www.widget.natuvea.com/index.js';

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
}); 
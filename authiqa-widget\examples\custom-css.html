<!DOCTYPE html>
<html>
<head>
    <title>Sign Up - Authiqa Widget</title>
    <script
        src="../dist/index.js"
        defer
        data-api-key="APK_c390e81dffc818d12450bb2775266219_1745408324"
        action="signup"
        verifyAuthPath="https://natuvea.github.io/childdemo/verify.html"
        resendAuthPath="https://natuvea.github.io/childdemo/resend.html"
        termsAndConditions="https://www.mywebsite.com/terms"
        privacy="https://www.mywebsite.com/privacy"
        notificationSettings="https://www.mywebsite.com/notification"
        data-messages='{
            "signupSuccess": "Account created successfully! Please check your email for verification.",
            "signupLoading": "Creating your account..."
        }'
        data-customization='{
            "pageLayout": {
                "backgroundColor": "#ffffff",
                "formPosition": "center",
                "formMarginTop": "50px",
                "formMarginBottom": "50px"
            },
            "layout": {
                "padding": "3.5rem",
                "margin": "3rem",
                "borderRadius": "7.5rem",
                "maxWidth": "19.5rem"
            },
            "colors": {
                "background": "#723c3c",
                "buttonBackground": "#5b1f1f",
                "buttonText": "#0400ff",
                "inputBackground": "#a87171",
                "inputText": "#ffffff",
                "inputPlaceholder": "#ffffff",
                "borderColor": "#061cc1"
            },
            "typography": {
                "titleText": {
                    "signupText": "Sign Up"
                },
                "titleSize": "2.8rem",
                "titleColor": "#ab6d6d",
                "labelSize": "2rem", 
                "fontFamily": "Arial, sans-serif",
                "termsText": {
                    "agreePrefix": "I agree with the",
                    "andConnector": "ao",
                    "defaultPrefix": "and default",
                    "linkText": {
                        "terms": "Terms and Conditions",
                        "privacy": "Privacy Policy",
                        "notifications": "Notification Settings"
                    },
                    "textColor": "#000000",
                    "linkColor": "#ffcc00"
                }
            },
            "inputs": {
                "emailLabel": "Email Address",
                "passwordLabel": "Password",
                "usernameLabel": "Username"
            },
            "buttons": {
                "signupText": "Sign Up",
                "height": "2.8125rem",
                "borderRadius": "7.5rem"
            }
        }'
    ></script>
</head>
<body>
    <div id="authiqa"></div>
</body>
</html>


function detectEnvironment(): 'staging' | 'production' {
    const scriptElement = document.querySelector('script[data-public-key]');
    const scriptSrc = scriptElement?.getAttribute('src') || '';

    if (scriptSrc.includes('staging.widget.authiqa.com')) {
        return 'staging';
    }
    return 'production';
}

export const getApiConfig = (organizationDomain: string) => {
    const environment = detectEnvironment();

    return {
        API_BASE: environment === 'staging'
            ? 'https://staging.api.authiqa.com'
            : (process.env.NODE_ENV === 'production'
                ? `https://api.${organizationDomain}`
                : 'https://api.authiqa.com'),
        ENDPOINTS: {
            ORGANIZATION_DETAILS: '/auth/organization-details',
            AUTH_STATUS: '/auth/status',

        }
    };
};
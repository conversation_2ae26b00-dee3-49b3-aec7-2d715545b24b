name: Deploy to authiqa.com S3 Bucket 

on:
  push:
    branches:
      - main
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      PROD_S3_BUCKET: widget.authiqa.com
      STAGING_S3_BUCKET: staging.widget.authiqa.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1 
          
      - name: Install Dependencies
        run: npm install

      - name: Build
        run: npm run build

      - name: Set Target S3 Bucket
        id: set-bucket
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "bucket=${PROD_S3_BUCKET}" >> $GITHUB_OUTPUT
          else
            echo "bucket=${STAGING_S3_BUCKET}" >> $GITHUB_OUTPUT
          fi

      - name: Sync files to S3 Bucket
        run: |
          aws s3 sync dist s3://${{ steps.set-bucket.outputs.bucket }} --delete

      - name: Output S3 Bucket Reference
        run: |
          echo "Deployment completed to S3 Bucket: ${{ steps.set-bucket.outputs.bucket }}"
